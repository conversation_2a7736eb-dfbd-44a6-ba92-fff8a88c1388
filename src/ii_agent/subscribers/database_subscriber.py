import logging
import uuid
from typing import Optional

from ii_agent.core.event import Realtime<PERSON>vent, EventType
from ii_agent.db.manager import Events
from ii_agent.server.shared import file_service


class DatabaseSubscriber:
    """Subscriber that handles database storage for events."""

    def __init__(self, session_id: Optional[uuid.UUID], logger: logging.Logger = None):
        self.session_id = session_id
        self._logger = logger or logging.getLogger(__name__)

    async def handle_event(self, event: RealtimeEvent) -> None:
        """Handle an event by saving it to the database."""
        # Save all events to database if we have a session
        if self.session_id is not None:

            # TODO: make this more generic
            if event.type == EventType.TOOL_RESULT and event.content.get("result", {}).get("type") == "file_url":
                tool_result = event.content.get("result")
                file_data = await file_service.write_file_from_url(
                    url=tool_result["url"],
                    file_name=tool_result["name"],
                    file_size=tool_result["size"],
                    content_type=tool_result["mime_type"],
                    session_id=str(self.session_id),
                )
                event.content["result"]["file_id"] = file_data.id
                event.content["result"]["file_storage_path"] = file_data.storage_path

            await Events.save_event(self.session_id, event)
        else:
            self._logger.info(f"No session ID, skipping event: {event}")

    def update_session_id(self, session_id: Optional[uuid.UUID]) -> None:
        """Update the session ID."""
        self.session_id = session_id
