"""Agent type configuration and enums."""

from enum import Enum
from typing import List

from ii_tool.tools.shell import ShellView, ShellInit, ShellKill, ShellStopCommand, ShellList, ShellRunCommand
from ii_tool.tools.file_system import GlobTool, GrepTool, FileEditTool, FileReadTool, FileWriteTool, LSTool, MultiEditTool
from ii_tool.tools.media import <PERSON>GenerateTool, ImageGenerateTool
from ii_tool.tools.dev import RegisterPort, FullStackInitTool
from ii_tool.tools.web import WebSearchTool, WebVisitTool
from ii_tool.tools.productivity import TodoReadTool, TodoWriteTool
from ii_tool.tools.agent.agent_tool import AgentTool


class AgentType(str, Enum):
    """Enumeration of available agent types."""

    GENERAL = "general"
    VIDEO_GENERATE = "video_generate"
    IMAGE = "image"
    SLIDE = "slide"
    WEBSITE_BUILD = "website_build"


class AgentTypeConfig:
    """Configuration for different agent types."""

    # Define toolsets for each agent type
    TOOLSETS = {
        AgentType.GENERAL: [
            ShellView.name,
            ShellInit.name,
            ShellKill.name,
            ShellStopCommand.name,
            ShellList.name,
            ShellRunCommand.name,
            GlobTool.name,
            GrepTool.name,
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            RegisterPort.name,
            FullStackInitTool.name,
        ],
        AgentType.VIDEO_GENERATE: [
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            VideoGenerateTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.IMAGE: [
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.SLIDE: [
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            ImageGenerateTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
        ],
        AgentType.WEBSITE_BUILD: [
            FileEditTool.name,
            FileReadTool.name,
            FileWriteTool.name,
            LSTool.name,
            MultiEditTool.name,
            ShellView.name,
            ShellInit.name,
            ShellKill.name,
            ShellStopCommand.name,
            ShellList.name,
            ShellRunCommand.name,
            GlobTool.name,
            GrepTool.name,
            LSTool.name,
            WebSearchTool.name,
            WebVisitTool.name,
            TodoReadTool.name,
            TodoWriteTool.name,
            RegisterPort.name,
            FullStackInitTool.name,
        ],
    }

    @classmethod
    def get_toolset(cls, agent_type: AgentType) -> List[str]:
        """Get the toolset for a specific agent type."""
        return cls.TOOLSETS.get(agent_type, cls.TOOLSETS[AgentType.GENERAL])

    @classmethod
    def is_valid_agent_type(cls, agent_type: str) -> bool:
        """Check if an agent type is valid."""
        try:
            AgentType(agent_type)
            return True
        except ValueError:
            return False

    @classmethod
    def get_all_agent_types(cls) -> List[str]:
        """Get all available agent types."""
        return [agent_type.value for agent_type in AgentType]
