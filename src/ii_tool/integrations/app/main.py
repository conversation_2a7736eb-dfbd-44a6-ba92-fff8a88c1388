import uvicorn
import argparse
from fastapi import FastAP<PERSON>
from pydantic import BaseModel, Field
from typing import Literal, List, Dict, Any
from .config import config
from ii_tool.integrations.image_generation import create_image_generation_client
from ii_tool.integrations.web_search import create_web_search_client
from ii_tool.integrations.image_search import create_image_search_client
from ii_tool.integrations.web_visit import create_web_visit_client
from ii_tool.integrations.video_generation import create_video_generation_client


app = FastAPI()


# --- Image Generation ---

class ImageGenerationRequest(BaseModel):
    prompt: str
    aspect_ratio: Literal["1:1", "16:9", "9:16", "4:3", "3:4"] = "1:1"

class ImageGenerationResponse(BaseModel):
    url: str
    size: int
    mime_type: str
    

@app.post("/image-generation", response_model=ImageGenerationResponse)
async def generate_image(
    request: ImageGenerationRequest,
    ):
    """Generate image from text prompt."""

    client = create_image_generation_client(config.image_generate_config)
    image_result = await client.generate_image(
        prompt=request.prompt,
        aspect_ratio=request.aspect_ratio
    )
    return ImageGenerationResponse(
        url=image_result.url,
        size=image_result.size,
        mime_type=image_result.mime_type,
    )


# --- Video Generation ---

class VideoGenerationRequest(BaseModel):
    prompt: str
    aspect_ratio: Literal["16:9", "9:16"] = "16:9"
    duration_seconds: int = Field(..., ge=5, le=8)
    image_base64: str | None = None
    image_mime_type: Literal["image/png", "image/jpeg", "image/webp"] | None = None

class VideoGenerationResponse(BaseModel):
    url: str
    size: int
    mime_type: str

@app.post("/video-generation", response_model=VideoGenerationResponse)
async def video_generation(
    request: VideoGenerationRequest,
    ):
    """Generate video from text prompt or/and image."""

    client = create_video_generation_client(config.video_generate_config)
    video_result = await client.generate_video(
        prompt=request.prompt,
        aspect_ratio=request.aspect_ratio,
        duration_seconds=request.duration_seconds,
        image_base64=request.image_base64,
        image_mime_type=request.image_mime_type,
    )
    return VideoGenerationResponse(
        url=video_result.url,
        size=video_result.size,
        mime_type=video_result.mime_type,
    )


# --- Web Search ---

class WebSearchRequest(BaseModel):
    query: str
    max_results: int = 5

class WebSearchResponse(BaseModel):
    results: List[Dict[str, Any]]

@app.post("/web-search", response_model=WebSearchResponse)
async def web_search(request: WebSearchRequest):
    """Perform web search using configured providers."""
    client = create_web_search_client(config.web_search_config)
    results = await client.search(request.query, request.max_results)
    return WebSearchResponse(results=results)


# --- Image Search ---

class ImageSearchRequest(BaseModel):
    query: str

class ImageSearchResponse(BaseModel):
    results: List[Dict[str, Any]]

@app.post("/image-search", response_model=ImageSearchResponse)
async def image_search(request: ImageSearchRequest):
    """Perform image search using configured providers."""
    client = create_image_search_client(config.image_search_config)
    results = await client.search(request.query)
    return ImageSearchResponse(results=results)


# --- Web Visit ---

class WebVisitRequest(BaseModel):
    url: str

class WebVisitResponse(BaseModel):
    content: str

@app.post("/web-visit", response_model=WebVisitResponse)
async def web_visit(request: WebVisitRequest):
    """Visit a web page and extract content."""
    client = create_web_visit_client(config.web_visit_config)
    content = await client.extract(request.url)
    return WebVisitResponse(content=content)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the tool server")
    parser.add_argument("--port", type=int, default=7000, help="Port to run the server on")
    args = parser.parse_args()

    uvicorn.run(app, host="0.0.0.0", port=args.port)