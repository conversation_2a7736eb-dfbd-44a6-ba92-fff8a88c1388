# src/ii_agent/tools/video_generate_from_text_tool.py
import base64
import os
import httpx
from pathlib import Path
from typing import Any

from ii_tool.tools.base import BaseTool, ToolR<PERSON>ult, FileURLContent

_EXTENSION_TO_MIMETYPE = {
    "png": "image/png",
    "jpg": "image/jpeg",
    "jpeg": "image/jpeg",
    "webp": "image/webp",
}

class VideoGenerateTool(BaseTool):
    name = "generate_video"
    display_name = "Generate Video"
    description = """Generates a short video based on a text prompt using Google's Veo 2 model via Vertex AI or Google AI Studio.
The generated video will be saved to the specified local path in the workspace.
Uses Google AI Studio if GEMINI_API_KEY is set, otherwise falls back to Vertex AI if configured."""
    input_schema = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "A detailed description of the video to be generated.",
            },
            "output_path": {
                "type": "string",
                "description": "The absolute path for the output MP4 video file within the workspace (e.g., '/workspace/generated_videos/my_video.mp4'). Must end with .mp4.",
            },
            "aspect_ratio": {
                "type": "string",
                "enum": ["16:9", "9:16"],
                "default": "16:9",
                "description": "The aspect ratio for the generated video.",
            },
            "duration_seconds": {
                "type": "string",
                "enum": ["5", "6", "7", "8"],
                "default": "5",
                "description": "The duration of the video in seconds.",
            },
            "input_image_path": {
                "type": "string",
                "description": "The absolute path to the input image file. If provided, the video will be started from the image.",
            },
        },
        "required": ["prompt", "output_path"],
    }
    read_only = True

    def __init__(
        self, tool_server_url: str
    ):
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        prompt = tool_input["prompt"]
        output_path = tool_input["output_path"]
        aspect_ratio = tool_input.get("aspect_ratio", "16:9")
        duration_seconds = int(tool_input.get("duration_seconds", "5"))
        input_image_path = tool_input.get("input_image_path", None)

        if not output_path.lower().endswith(".mp4"):
            return ToolResult(llm_content=f"Error: output_path: `{output_path}` must end with .mp4", is_error=True)

        output_path = Path(output_path).resolve()
        output_path.parent.mkdir(parents=True, exist_ok=True)

        payload = {
            "prompt": prompt,
            "aspect_ratio": aspect_ratio,
            "duration_seconds": duration_seconds,
            "input_image_path": input_image_path,
        }

        if input_image_path:
            if not os.path.exists(input_image_path):
                return ToolResult(llm_content=f"Error: input_image_path: `{input_image_path}` does not exist", is_error=True)

            with open(input_image_path, "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode("utf-8")
                image_extension = input_image_path.split(".")[-1].lower()
                image_mime_type = _EXTENSION_TO_MIMETYPE.get(image_extension)
                if not image_mime_type:
                    return ToolResult(llm_content=f"Error: Unsupported image format: {image_extension}. Supported formats: {', '.join(_EXTENSION_TO_MIMETYPE.keys())}", is_error=True)
                payload["image_base64"] = image_base64
                payload["image_mime_type"] = image_mime_type
        
        # generate the video
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.tool_server_url}/video-generation",
                json=payload,
                timeout=180,
            )
            response.raise_for_status()
            response_data = response.json()
        
        video_url = response_data["url"]
        video_mime_type = response_data["mime_type"]
        video_size = response_data["size"]

        # download the video
        async with httpx.AsyncClient() as download_client:
            download_response = await download_client.get(video_url)
            download_response.raise_for_status()

            with open(output_path, "wb") as f:
                f.write(download_response.content)

        return ToolResult(
            llm_content=f"Video generated and saved to {output_path}",
            user_display_content=FileURLContent(
                type="file_url",
                url=video_url,
                mime_type=video_mime_type,
                name=output_path.name,
                size=video_size,
            ).model_dump(),
        )

    async def execute_mcp_wrapper(self, 
        prompt: str, 
        output_path: str, 
        aspect_ratio: str = "16:9",
        duration_seconds: int = 5, 
        input_image_path: str | None = None,
    ):        
        return await self._mcp_wrapper(
            tool_input={
                "prompt": prompt,
                "output_path": output_path,
                "aspect_ratio": aspect_ratio,
                "duration_seconds": duration_seconds,
                "input_image_path": input_image_path,
            }
        )

# class LongVideoGenerateFromTextTool(BaseTool):
#     name = "generate_long_video_from_text"
#     display_name = "Generate Long Video"
#     description = """Generates a long video (>= 10 seconds) based on a sequence of text prompts. Each prompt presents a new scene in the video, each scene is minimum 5 and maximum 8 seconds (preferably 5 seconds). Video is combined sequentially from the first scene to the last.
# The generated video will be saved to the specified local path in the workspace."""
#     input_schema = {
#         "type": "object",
#         "properties": {
#             "prompts": {
#                 "type": "array",
#                 "items": {
#                     "type": "string",
#                     "description": "A description of a scene in the video.",
#                 },
#                 "description": "A sequence of detailed descriptions of the video to be generated. Each prompt presents a scene in the video.",
#             },
#             "output_path": {
#                 "type": "string",
#                 "description": "The desired relative path for the output MP4 video file within the workspace (e.g., 'generated_videos/my_video.mp4'). Must end with .mp4.",
#             },
#             "aspect_ratio": {
#                 "type": "string",
#                 "enum": ["16:9", "9:16"],
#                 "default": "16:9",
#                 "description": "The aspect ratio for the generated video.",
#             },
#             "duration_seconds": {
#                 "type": "string",
#                 "description": "The total duration of the video will be the sum of the duration of all scenes. The duration of each scene is determined by the model.",
#             },
#             "enhance_prompt": {
#                 "type": "boolean",
#                 "default": True,
#                 "description": "Whether to enhance the provided prompt for better results.",
#             },
#         },
#         "required": ["prompts", "output_path", "duration_seconds"],
#     }
#     read_only = True

#     def __init__(
#         self, workspace_manager: WorkspaceManager, settings: VideoGenerateConfig
#     ):
#         super().__init__()
#         self.workspace_manager = workspace_manager
#         self.settings = settings

#     async def execute(
#         self,
#         tool_input: dict[str, Any],
#     ) -> ToolResult:
#         prompts = tool_input["prompts"]
#         relative_output_path = tool_input["output_path"]
#         aspect_ratio = tool_input.get("aspect_ratio", "16:9")
#         duration_seconds = int(tool_input["duration_seconds"])
#         enhance_prompt = tool_input.get("enhance_prompt", True)

#         if not relative_output_path.lower().endswith(".mp4"):
#             return ToolResult(
#                 llm_content="Error: output_path must end with .mp4",
#                 user_display_content="Invalid output filename for video.",
#                 is_error=True,
#             )

#         if len(prompts) == 0:
#             return ToolResult(
#                 llm_content="Error: At least one prompt is required",
#                 user_display_content="No prompts provided for video generation.",
#                 is_error=True,
#             )

#         local_output_path = Path(relative_output_path).resolve()
#         local_output_path.parent.mkdir(parents=True, exist_ok=True)

#         # Create temporary directory for scene videos and frames
#         temp_dir = local_output_path.parent / f"temp_{uuid.uuid4().hex}"
#         temp_dir.mkdir(exist_ok=True)

#         scene_video_paths = []

#         try:
#             # Calculate duration per scene
#             duration_per_scene = max(5, duration_seconds // len(prompts))
#             if duration_per_scene > 8:
#                 duration_per_scene = 8

#             # Generate first scene from text
#             first_scene_filename = "scene_0.mp4"
#             first_scene_path = temp_dir / first_scene_filename

#             text_tool = VideoGenerateTool(self.workspace_manager, self.settings)
#             first_scene_result = await text_tool.run_impl(
#                 {
#                     "prompt": prompts[0],
#                     "output_path": str(first_scene_path),
#                     "aspect_ratio": aspect_ratio,
#                     "duration_seconds": str(duration_per_scene),
#                     "enhance_prompt": enhance_prompt,
#                     "allow_person_generation": True,
#                 }
#             )

#             if not first_scene_result.auxiliary_data.get("success", False):
#                 return ToolResult(
#                     llm_content=f"Error generating first scene: {first_scene_result.auxiliary_data.get('error', 'Unknown error')}",
#                     user_display_content="Failed to generate first scene.",
#                     is_error=True,
#                 )

#             scene_video_paths.append(first_scene_path)

#             # Generate subsequent scenes from last frame + prompt
#             image_tool = VideoGenerateFromImageTool(
#                 self.workspace_manager, self.settings
#             )

#             for i, prompt in enumerate(prompts[1:], 1):
#                 # Extract last frame from previous scene
#                 prev_video_path = scene_video_paths[-1]
#                 last_frame_path = temp_dir / f"last_frame_{i - 1}.png"

#                 # Use ffmpeg to extract last frame
#                 extract_cmd = [
#                     "ffmpeg",
#                     "-i",
#                     str(prev_video_path),
#                     "-vf",
#                     "select=eq(n\\,0)",
#                     "-q:v",
#                     "3",
#                     "-vframes",
#                     "1",
#                     "-f",
#                     "image2",
#                     str(last_frame_path),
#                     "-y",
#                 ]

#                 # Actually extract the very last frame
#                 extract_cmd = [
#                     "ffmpeg",
#                     "-sseof",
#                     "-1",
#                     "-i",
#                     str(prev_video_path),
#                     "-update",
#                     "1",
#                     "-q:v",
#                     "1",
#                     str(last_frame_path),
#                     "-y",
#                 ]

#                 subprocess.run(extract_cmd, check=True, capture_output=True)

#                 # Generate next scene from last frame + prompt
#                 scene_filename = f"scene_{i}.mp4"
#                 scene_path = temp_dir / scene_filename

#                 scene_result = await image_tool.run_impl(
#                     {
#                         "image_file_path": str(last_frame_path),
#                         "output_path": str(scene_path),
#                         "prompt": prompt,
#                         "aspect_ratio": aspect_ratio,
#                         "duration_seconds": str(duration_per_scene),
#                         "allow_person_generation": True,
#                     }
#                 )

#                 if not scene_result.auxiliary_data.get("success", False):
#                     return ToolResult(
#                         llm_content=f"Error generating scene {i}: {scene_result.auxiliary_data.get('error', 'Unknown error')}",
#                         user_display_content=f"Failed to generate scene {i}.",
#                         is_error=True,
#                     )

#                 scene_video_paths.append(scene_path)

#             # Combine all scenes into final video
#             if len(scene_video_paths) == 1:
#                 # Only one scene, just copy it
#                 shutil.copy2(scene_video_paths[0], local_output_path)
#             else:
#                 # Create file list for ffmpeg concat
#                 concat_file = temp_dir / "concat_list.txt"
#                 with open(concat_file, "w") as f:
#                     for video_path in scene_video_paths:
#                         f.write(f"file '{video_path.absolute()}'\n")

#                 # Concatenate videos
#                 concat_cmd = [
#                     "ffmpeg",
#                     "-f",
#                     "concat",
#                     "-safe",
#                     "0",
#                     "-i",
#                     str(concat_file),
#                     "-c",
#                     "copy",
#                     str(local_output_path),
#                     "-y",
#                 ]

#                 subprocess.run(concat_cmd, check=True, capture_output=True)

#             return ToolResult(
#                 llm_content=f"Successfully generated long video with {len(prompts)} scenes and saved to '{relative_output_path}'",
#                 user_display_content=f"Long video with {len(prompts)} scenes generated and saved to {relative_output_path}",
#                 is_error=False,
#             )

#         except Exception as e:
#             return ToolResult(
#                 llm_content=f"Error generating long video: {str(e)}",
#                 user_display_content="Failed to generate long video.",
#                 is_error=True,
#             )
#         finally:
#             # Clean up temporary files
#             if temp_dir.exists():
#                 try:
#                     shutil.rmtree(temp_dir)
#                 except Exception as e_cleanup:
#                     print(
#                         f"Warning: Failed to clean up temporary directory {temp_dir}: {e_cleanup}"
#                     )

#     async def execute_mcp_wrapper(self, 
#         prompts: list[str], 
#         output_path: str, 
#         aspect_ratio: str = "16:9", 
#         duration_seconds: int = 5, 
#         enhance_prompt: bool = True
#     ):        
#         return await self._mcp_wrapper(
#             tool_input={
#                 "prompts": prompts,
#                 "output_path": output_path,
#                 "aspect_ratio": aspect_ratio,
#                 "duration_seconds": duration_seconds,
#                 "enhance_prompt": enhance_prompt,
#             }
#         )

# class LongVideoGenerateFromImageTool(BaseTool):
#     name = "generate_long_video_from_image"
#     display_name = "Generate Long Video from Image"
#     description = """Generates a long video (>= 10 seconds) based on input image and a sequence of text prompts. Each prompt presents a new scene in the video, each scene is minimum 5 and maximum 8 seconds (preferably 5 seconds). Video is combined sequentially from the first scene to the last.
# The generated video will be saved to the specified local path in the workspace."""
#     input_schema = {
#         "type": "object",
#         "properties": {
#             "image_file_path": {
#                 "type": "string",
#                 "description": "The relative path to the input image file within the workspace (e.g., 'uploads/my_image.png').",
#             },
#             "prompts": {
#                 "type": "array",
#                 "items": {
#                     "type": "string",
#                     "description": "A description of a scene in the video.",
#                 },
#                 "description": "A sequence of detailed descriptions of the video to be generated. Each prompt presents a scene in the video.",
#             },
#             "output_path": {
#                 "type": "string",
#                 "description": "The desired relative path for the output MP4 video file within the workspace (e.g., 'generated_videos/my_video.mp4'). Must end with .mp4.",
#             },
#             "aspect_ratio": {
#                 "type": "string",
#                 "enum": ["16:9", "9:16"],
#                 "default": "16:9",
#                 "description": "The aspect ratio for the generated video.",
#             },
#             "duration_seconds": {
#                 "type": "string",
#                 "description": "The total duration of the video will be the sum of the duration of all scenes. The duration of each scene is determined by the model.",
#             },
#             "enhance_prompt": {
#                 "type": "boolean",
#                 "default": True,
#                 "description": "Whether to enhance the provided prompt for better results.",
#             },
#         },
#         "required": [
#             "image_file_path",
#             "prompts",
#             "output_path",
#             "duration_seconds",
#         ],
#     }
#     read_only = True

#     def __init__(
#         self, workspace_manager: WorkspaceManager, settings: VideoGenerateConfig
#     ):
#         super().__init__()
#         self.workspace_manager = workspace_manager
#         self.settings = settings

#     async def execute(
#         self,
#         tool_input: dict[str, Any],
#     ) -> ToolResult:
#         image_file_path = tool_input["image_file_path"]
#         prompts = tool_input["prompts"]
#         relative_output_path = tool_input["output_path"]
#         aspect_ratio = tool_input.get("aspect_ratio", "16:9")
#         duration_seconds = int(tool_input["duration_seconds"])
#         enhance_prompt = tool_input.get("enhance_prompt", True)

#         if not relative_output_path.lower().endswith(".mp4"):
#             return ToolResult(
#                 llm_content="Error: output_path must end with .mp4",
#                 user_display_content="Invalid output filename for video.",
#                 is_error=True,
#             )

#         if len(prompts) == 0:
#             return ToolResult(
#                 llm_content="Error: At least one prompt is required",
#                 user_display_content="No prompts provided for video generation.",
#                 is_error=True,
#             )

#         local_output_path = Path(relative_output_path).resolve()
#         local_output_path.parent.mkdir(parents=True, exist_ok=True)

#         # Create temporary directory for scene videos and frames
#         temp_dir = local_output_path.parent / f"temp_{uuid.uuid4().hex}"
#         temp_dir.mkdir(exist_ok=True)

#         scene_video_paths = []

#         try:
#             # Calculate duration per scene
#             duration_per_scene = max(5, duration_seconds // len(prompts))
#             if duration_per_scene > 8:
#                 duration_per_scene = 8

#             # Generate first scene from text
#             first_scene_filename = "scene_0.mp4"
#             first_scene_path = temp_dir / first_scene_filename

#             image_tool = VideoGenerateFromImageTool(
#                 self.workspace_manager, self.settings
#             )
#             first_scene_result = await image_tool.run_impl(
#                 {
#                     "image_file_path": image_file_path,
#                     "prompt": prompts[0],
#                     "output_path": str(first_scene_path),
#                     "aspect_ratio": aspect_ratio,
#                     "duration_seconds": str(duration_per_scene),
#                     "enhance_prompt": enhance_prompt,
#                     "allow_person_generation": True,
#                 }
#             )

#             if not first_scene_result.auxiliary_data.get("success", False):
#                 return ToolResult(
#                     llm_content=f"Error generating first scene: {first_scene_result.auxiliary_data.get('error', 'Unknown error')}",
#                     user_display_content="Failed to generate first scene.",
#                     is_error=True,
#                 )

#             scene_video_paths.append(first_scene_path)

#             for i, prompt in enumerate(prompts[1:], 1):
#                 # Extract last frame from previous scene
#                 prev_video_path = scene_video_paths[-1]
#                 last_frame_path = temp_dir / f"last_frame_{i - 1}.png"

#                 # Use ffmpeg to extract last frame
#                 extract_cmd = [
#                     "ffmpeg",
#                     "-i",
#                     str(prev_video_path),
#                     "-vf",
#                     "select=eq(n\\,0)",
#                     "-q:v",
#                     "3",
#                     "-vframes",
#                     "1",
#                     "-f",
#                     "image2",
#                     str(last_frame_path),
#                     "-y",
#                 ]

#                 # Actually extract the very last frame
#                 extract_cmd = [
#                     "ffmpeg",
#                     "-sseof",
#                     "-1",
#                     "-i",
#                     str(prev_video_path),
#                     "-update",
#                     "1",
#                     "-q:v",
#                     "1",
#                     str(last_frame_path),
#                     "-y",
#                 ]

#                 subprocess.run(extract_cmd, check=True, capture_output=True)

#                 # Generate next scene from last frame + prompt
#                 scene_filename = f"scene_{i}.mp4"
#                 scene_path = temp_dir / scene_filename

#                 scene_result = await image_tool.run_impl(
#                     {
#                         "image_file_path": str(last_frame_path),
#                         "output_path": str(scene_path),
#                         "prompt": prompt,
#                         "aspect_ratio": aspect_ratio,
#                         "duration_seconds": str(duration_per_scene),
#                         "allow_person_generation": True,
#                     }
#                 )

#                 if not scene_result.auxiliary_data.get("success", False):
#                     return ToolResult(
#                         llm_content=f"Error generating scene {i}: {scene_result.auxiliary_data.get('error', 'Unknown error')}",
#                         user_display_content=f"Failed to generate scene {i}.",
#                         is_error=True,
#                     )

#                 scene_video_paths.append(scene_path)

#             # Combine all scenes into final video
#             if len(scene_video_paths) == 1:
#                 # Only one scene, just copy it
#                 shutil.copy2(scene_video_paths[0], local_output_path)
#             else:
#                 # Create file list for ffmpeg concat
#                 concat_file = temp_dir / "concat_list.txt"
#                 with open(concat_file, "w") as f:
#                     for video_path in scene_video_paths:
#                         f.write(f"file '{video_path.absolute()}'\n")

#                 # Concatenate videos
#                 concat_cmd = [
#                     "ffmpeg",
#                     "-f",
#                     "concat",
#                     "-safe",
#                     "0",
#                     "-i",
#                     str(concat_file),
#                     "-c",
#                     "copy",
#                     str(local_output_path),
#                     "-y",
#                 ]

#                 subprocess.run(concat_cmd, check=True, capture_output=True)

#             return ToolResult(
#                 llm_content=f"Successfully generated long video with {len(prompts)} scenes and saved to '{relative_output_path}'",
#                 user_display_content=f"Long video with {len(prompts)} scenes generated and saved to {relative_output_path}",
#                 is_error=False,
#             )

#         except Exception as e:
#             return ToolResult(
#                 llm_content=f"Error generating long video: {str(e)}",
#                 user_display_content="Failed to generate long video.",
#                 is_error=True,
#             )
#         finally:
#             # Clean up temporary files
#             if temp_dir.exists():
#                 try:
#                     shutil.rmtree(temp_dir)
#                 except Exception as e_cleanup:
#                     print(
#                         f"Warning: Failed to clean up temporary directory {temp_dir}: {e_cleanup}"
#                     )

#     async def execute_mcp_wrapper(self, 
#         image_file_path: str, 
#         output_path: str, 
#         prompts: list[str], 
#         aspect_ratio: str = "16:9", 
#         duration_seconds: int = 5, 
#         enhance_prompt: bool = True
#     ):        
#         return await self._mcp_wrapper(
#             tool_input={
#                 "image_file_path": image_file_path,
#                 "output_path": output_path,
#                 "prompts": prompts,
#                 "aspect_ratio": aspect_ratio,
#                 "duration_seconds": duration_seconds,
#                 "enhance_prompt": enhance_prompt,
#             }
#         )