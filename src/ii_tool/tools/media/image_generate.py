# src/ii_agent/tools/image_generate_tool.py

import os
import httpx

from typing import Any
from pathlib import Path
from ii_tool.tools.base import BaseTool, ToolResult, FileURLContent


# Name
NAME = "generate_image"
DISPLAY_NAME = "Generate Image"

# Tool description
DESCRIPTION = """Generates an image based on a text prompt and the generated image will be saved to the specified local path in the workspace as a PNG file."""

# Input schema
INPUT_SCHEMA = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "A detailed description of the image to be generated.",
            },
            "output_path": {
                "type": "string",
                "description": "The desired relative path for the output PNG image file within the workspace (e.g., 'generated_images/my_image.png'). Must end with .png.",
            },
            "aspect_ratio": {
                "type": "string",
                "enum": ["1:1", "16:9", "9:16", "4:3", "3:4"],
                "default": "1:1",
                "description": "The aspect ratio for the generated image.",
            },
        },
        "required": ["prompt", "output_path"],
    }

class ImageGenerateTool(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    def __init__(self, tool_server_url: str):
        super().__init__()
        self.tool_server_url = tool_server_url

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        output_path = tool_input["output_path"]
        
        if not output_path.lower().endswith(".png"):
            return ToolResult(
                llm_content="Output path must end with .png"
            )

        output_path = Path(output_path).resolve()
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # generate the image
        async with httpx.AsyncClient() as client:
            payload = {
                "prompt": tool_input["prompt"],
                "aspect_ratio": tool_input["aspect_ratio"],
            }
            response = await client.post(
                f"{self.tool_server_url}/image-generation",
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            response_data = response.json()

        image_url = response_data["url"]
        image_mime_type = response_data["mime_type"]
        image_size = response_data["size"]

        # download the image
        async with httpx.AsyncClient() as download_client:
            download_response = await download_client.get(image_url)
            download_response.raise_for_status()
            
            with open(output_path, "wb") as f:
                f.write(download_response.content)

        return ToolResult(
            llm_content=f"Image generated and saved to {output_path}",
            user_display_content=FileURLContent(
                type="file_url",
                url=image_url,
                mime_type=image_mime_type,
                name=output_path.name,
                size=image_size,
            ).model_dump(),
        )
            
    async def execute_mcp_wrapper(
        self,
        prompt: str,
        output_path: str,
        aspect_ratio: str = "1:1",
    ):
        return await self._mcp_wrapper(
            tool_input={
                "prompt": prompt,
                "output_path": output_path,
                "aspect_ratio": aspect_ratio,
            }
        )