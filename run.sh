export DATABASE_URL="sqlite+aiosqlite:///./ii_agent.db"
export GOOGLE_CLIENT_ID=*************-n4nh12r24qlobbm2lmp7k0864ladr4lt.apps.googleusercontent.com
export GOOGLE_CLIENT_SECRET=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
export OAUTHLIB_INSECURE_TRANSPORT=1
export GOOGLE_DISCOVERY_URL=http://localhost:8000/auth/oauth/google/callback
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/Documents/ii_agent_vertex_ai_service_account.json"

# Complex types like list, set, dict, and sub-models are populated from the environment by treating the environment variable's value as a JSON-encoded string.

export LLM_CONFIGS='{"gemini/gemini-2.5-pro":{"model":"gemini-2.5-pro","api_key":"AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA","base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"vertex/claude-sonnet-4@********":{"model":"claude-sonnet-4@********","api_key":null,"base_url":null,"max_retries":3,"max_message_chars":30000,"temperature":0.0,"vertex_region":"us-east5","vertex_project_id":"backend-alpha-97077","api_type":"anthropic","thinking_tokens":10000,"azure_endpoint":null,"azure_api_version":null,"cot_model":false},"default":{"model":"gemini-2.5-pro","api_key":"AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA","base_url":null,"max_retries":10,"max_message_chars":30000,"temperature":0.0,"vertex_region":null,"vertex_project_id":null,"api_type":"gemini","thinking_tokens":0,"azure_endpoint":null,"azure_api_version":null,"cot_model":false}}'

export AUTO_APPROVE_TOOLS=true
export ACCESS_TOKEN_EXPIRE_MINUTES=43200
export WEB_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"
export IMAGE_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"
export WEB_VISIT_FIRECRAWL_API_KEY="fc-9e8a7d0491914b28830b555942769bb4"

export SANDBOX_E2B_API_KEY="e2b_f1f33fac809b1a2f5b2e7804e5da41f8918405ac"
# export SANDBOX_E2B_TEMPLATE_ID="uytn478ocgj7ekv81r5v"
# export SANDBOX_E2B_TEMPLATE_ID=alyrjtfjle0yf37uxqdf
export SANDBOX_E2B_TEMPLATE_ID=hw1mt8ikm16ml8ayd0bx
export SANDBOX_REDIS_URL=rediss://default:<EMAIL>:6379

export STORAGE_PROVIDER="gcs"
export FILE_UPLOAD_PROJECT_ID="backend-alpha-97077"
export FILE_UPLOAD_BUCKET_NAME="ii-agent-dev"
export AVATAR_PROJECT_ID="backend-alpha-97077"
export AVATAR_BUCKET_NAME="ii-agent-user-avatar"



python ws_server.py