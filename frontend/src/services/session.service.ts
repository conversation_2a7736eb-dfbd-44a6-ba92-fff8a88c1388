import axiosInstance from '@/lib/axios'
import { ISession } from '@/typings/agent'
import {
    SessionsResponse,
    SessionEventsResponse,
    CreateSessionRequest,
    UpdateSessionRequest,
    SessionFile
} from '@/typings/session'

class SessionService {
    async getSessions(): Promise<ISession[]> {
        const response = await axiosInstance.get<SessionsResponse>(`/sessions`)
        return response.data.sessions || []
    }
    async getSession(sessionId: string): Promise<ISession> {
        const response = await axiosInstance.get<ISession>(
            `/sessions/${sessionId}`
        )
        return response.data
    }

    async getSessionEvents(sessionId: string): Promise<SessionEventsResponse> {
        const response = await axiosInstance.get<SessionEventsResponse>(
            `/sessions/${sessionId}/events`
        )
        return response.data
    }

    async getSessionFiles(sessionId: string): Promise<SessionFile[]> {
        const response = await axiosInstance.get<SessionFile[]>(
            `/sessions/${sessionId}/files`
        )
        return response.data
    }

    async createSession(data: CreateSessionRequest): Promise<ISession> {
        const response = await axiosInstance.post<ISession>('/sessions', data)
        return response.data
    }

    async deleteSession(sessionId: string): Promise<void> {
        await axiosInstance.delete(`/sessions/${sessionId}`)
    }

    async updateSession(
        sessionId: string,
        data: UpdateSessionRequest
    ): Promise<ISession> {
        const response = await axiosInstance.patch<ISession>(
            `/sessions/${sessionId}`,
            data
        )
        return response.data
    }
}

export const sessionService = new SessionService()
