import { AGENT_TYPE } from '@/typings'

export const INIT_TOOLS = [
    {
        name: 'Deep Research',
        description: 'Enable in-depth research capabilities',
        icon: 'search-status',
        isFill: true,
        isActive: false,
        isRequireKey: false
    },
    {
        name: 'Media Generation',
        description: 'Generate images and videos',
        icon: 'image',
        isFill: false,
        isActive: false,
        isRequireKey: true
    },
    {
        name: '<PERSON>rows<PERSON>',
        description: 'Enable web browsing capabilities',
        icon: 'browser',
        isFill: false,
        isActive: true,
        isRequireKey: false
    },
    {
        name: 'Review Agent',
        description: 'Enable reviewer agent to analyze and improve outputs',
        icon: 'review-agent',
        isFill: false,
        isActive: false,
        isRequireKey: false
    }
]

export const FEATURES = [
    {
        icon: 'gallery',
        name: 'Generate Image',
        type: AGENT_TYPE.IMAGE
    },
    {
        icon: 'video-square',
        name: 'Generate Video',
        type: AGENT_TYPE.VIDEO_GENERATE
    },
    {
        icon: 'note-2',
        name: 'Create Slide',
        type: AGENT_TYPE.SLIDE
    },
    {
        icon: 'monitor',
        name: 'Create a Website',
        type: AGENT_TYPE.WEBSITE_BUILD
    }
]
