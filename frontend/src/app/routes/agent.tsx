import Lottie from 'lottie-react'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router'

import <PERSON><PERSON><PERSON><PERSON> from '@/assets/thinking.json'
import AgentBuild from '@/components/agent/agent-build'
import AgentSteps from '@/components/agent/agent-step'
import AgentTabs from '@/components/agent/agent-tab'
import AgentTasks from '@/components/agent/agent-task'
import Cha<PERSON><PERSON><PERSON> from '@/components/agent/chat-box'
import AgentHeader from '@/components/agent/header'
import RightSidebar from '@/components/right-sidebar'
import { sessionService } from '@/services/session.service'
import {
    selectActiveTab,
    selectSelectedBuildStep,
    selectVscodeUrl,
    setSelectedFeature,
    useAppDispatch,
    useAppSelector
} from '@/state'
import { BUILD_STEP, ISession, TAB } from '@/typings/agent'
import <PERSON><PERSON><PERSON><PERSON> from '@/components/agent/agent-result'
import AgentPopoverDone from '@/components/agent/agent-popover-done'

function AgentPageContent() {
    const { sessionId } = useParams()
    const dispatch = useAppDispatch()

    const activeTab = useAppSelector(selectActiveTab)
    const vscodeUrl = useAppSelector(selectVscodeUrl)
    const selectedBuildStep = useAppSelector(selectSelectedBuildStep)
    const [sessionData, setSessionData] = useState<ISession>()

    useEffect(() => {
        let timeoutId: NodeJS.Timeout | undefined

        const fetchSession = async () => {
            if (sessionId) {
                const data = await sessionService.getSession(sessionId)

                if (!data?.name || data.name.trim() === '') {
                    // Retry after 5 seconds if name is null or empty
                    timeoutId = setTimeout(() => {
                        fetchSession()
                    }, 5000)
                } else {
                    dispatch(setSelectedFeature(data.agent_type))
                    setSessionData(data)
                }
            }
        }

        fetchSession()

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId)
            }
        }
    }, [sessionId])

    return (
        <div className="flex h-screen">
            <div className="flex-1">
                <AgentHeader sessionData={sessionData} />
                <div className="flex h-[calc(100vh-53px)] ">
                    <div
                        className={`flex-1 flex items-center justify-center ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? '' : 'hidden'}`}
                    >
                        <div className="flex flex-col items-center justify-center">
                            <Lottie
                                animationData={ThinkingLottie}
                                loop={true}
                            />
                            <p className="text-[32px] font-bold  text-black dark:text-sky-blue">
                                I’m thinking ...
                            </p>
                            <p className="text-2xl text-black dark:text-sky-blue mt-1">
                                get back to you in minutes
                            </p>
                        </div>
                    </div>

                    <div
                        className={`flex-1 flex flex-col h-full relative ${activeTab === TAB.BUILD && selectedBuildStep === BUILD_STEP.THINKING ? 'hidden' : ''}`}
                    >
                        <AgentTabs />
                        <div className="flex-1">
                            <div
                                className={
                                    activeTab === TAB.BUILD
                                        ? 'h-full'
                                        : 'hidden h-full'
                                }
                            >
                                <div
                                    className={`flex flex-col items-center justify-between p-6 pb-8 h-full`}
                                >
                                    <AgentSteps />
                                    <div
                                        className={`flex flex-1 flex-col justify-between w-full ${selectedBuildStep === BUILD_STEP.PLAN ? '' : 'hidden'}`}
                                    >
                                        <AgentTasks className="flex-1" />
                                        <div />
                                    </div>
                                    <AgentBuild
                                        className={
                                            selectedBuildStep ===
                                            BUILD_STEP.BUILD
                                                ? ''
                                                : 'hidden'
                                        }
                                    />
                                </div>
                            </div>

                            <iframe
                                src={vscodeUrl}
                                className={`w-full h-full ${activeTab === TAB.CODE ? '' : 'hidden'}`}
                            />

                            <div
                                className={`h-full relative ${activeTab === TAB.RESULT ? '' : 'hidden'}`}
                            >
                                <AgentResult />
                                <div className="absolute bottom-8 right-4">
                                    <AgentPopoverDone />
                                </div>
                            </div>
                        </div>
                    </div>
                    <ChatBox />
                </div>
            </div>
            <RightSidebar />
        </div>
    )
}

export function AgentPage() {
    return <AgentPageContent />
}

export const Component = AgentPage
