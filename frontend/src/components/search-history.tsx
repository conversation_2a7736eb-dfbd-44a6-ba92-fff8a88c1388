import { useMemo, useState } from 'react'
import dayjs from 'dayjs'
import { Link, useNavigate } from 'react-router'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
dayjs.extend(isSameOrAfter)

import { Icon } from './ui/icon'
import { Input } from './ui/input'
import {
    Sheet,
    SheetClose,
    SheetContent,
    SheetHeader,
    SheetTrigger
} from './ui/sheet'
import { Button } from './ui/button'
import { cn } from '@/lib/utils'
import {
    setCompleted,
    setMessages,
    selectSessions,
    useAppDispatch,
    useAppSelector,
    setBuildStep,
    setActiveTab,
    setStopped
} from '@/state'
import { BUILD_STEP, TAB } from '@/typings/agent'

interface SearchHistoryProps {
    className?: string
}

const SearchHistory = ({ className }: SearchHistoryProps) => {
    const [open, setOpen] = useState(false)

    const navigate = useNavigate()
    const dispatch = useAppDispatch()
    const sessions = useAppSelector(selectSessions)

    const [searchTerm, setSearchTerm] = useState('')

    const groupedSessions = useMemo(() => {
        const now = dayjs()
        const startOfToday = now.startOf('day')
        const startOfYesterday = startOfToday.subtract(1, 'day')
        const start7DaysAgo = startOfToday.subtract(7, 'days')

        const filteredSessions = [...sessions].filter((session) => {
            return (
                session.name &&
                session.name.toLowerCase().includes(searchTerm.toLowerCase())
            )
        })

        const todaySessions = filteredSessions.filter((session) => {
            return dayjs(session.created_at).isSame(startOfToday, 'day')
        })

        const yesterdaySessions = filteredSessions.filter((session) => {
            return dayjs(session.created_at).isSame(startOfYesterday, 'day')
        })

        const last7DaysSessions = filteredSessions.filter((session) => {
            const sessionDate = dayjs(session.created_at)
            return (
                sessionDate.isBefore(startOfYesterday) &&
                sessionDate.isSameOrAfter(start7DaysAgo)
            )
        })

        const last30DaysSessions = filteredSessions.filter((session) => {
            const sessionDate = dayjs(session.created_at)
            return sessionDate.isBefore(start7DaysAgo)
        })

        return {
            today: todaySessions,
            yesterday: yesterdaySessions,
            last_7_days: last7DaysSessions,
            last_30_days: last30DaysSessions
        }
    }, [sessions, searchTerm])

    const handleNewChat = () => {
        dispatch(setMessages([]))
        dispatch(setCompleted(false))
        dispatch(setActiveTab(TAB.BUILD))
        dispatch(setBuildStep(BUILD_STEP.THINKING))
        dispatch(setStopped(false))
        setOpen(false)
        navigate('/')
    }

    return (
        <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger className={className}>
                <div className="flex items-center gap-x-2 px-4 cursor-pointer">
                    <Icon
                        name="search-status"
                        className="fill-black dark:fill-white"
                    />
                    Search Chat
                </div>
            </SheetTrigger>
            <SheetContent
                side="left"
                className="px-6 pt-12 w-full !max-w-[560px]"
            >
                <SheetHeader className="p-0 gap-6 pb-6">
                    <div className="flex items-center justify-between">
                        <p className="text-2xl font-bold">Search</p>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="close"
                                    className="fill-grey-2 dark:fill-grey"
                                />
                            </SheetClose>
                        </div>
                    </div>
                    <div className="relative">
                        <Icon
                            name="search"
                            className="absolute left-4 top-3 size-6 fill-black dark:fill-white"
                        />
                        <Input
                            placeholder="Search chats ..."
                            value={searchTerm}
                            className="pl-[56px]"
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <Button
                        className="bg-firefly dark:bg-sky-blue w-full h-12 font-bold !text-sky-blue dark:!text-black rounded-xl"
                        onClick={handleNewChat}
                    >
                        <Icon
                            name="edit"
                            className="fill-sky-blue dark:fill-black size-6"
                        />
                        New Chat
                    </Button>
                </SheetHeader>
                <div className="space-y-6 dark:text-white overflow-auto pb-12">
                    {Object.entries(groupedSessions)
                        ?.filter(([key, value]) => {
                            return key && value.length > 0
                        })
                        .map(([key, value]) => (
                            <div key={key}>
                                <p className="text-lg font-bold">
                                    {key === 'today'
                                        ? 'Today'
                                        : key === 'yesterday'
                                          ? 'Yesterday'
                                          : key === 'last_7_days'
                                            ? 'Last 7 days'
                                            : 'Last 30 days'}
                                </p>
                                <div className="space-y-3 mt-3">
                                    {value.map((session) => (
                                        <Link
                                            key={session.id}
                                            to={`/${session.id}`}
                                            onClick={() => {
                                                dispatch(setMessages([]))
                                                dispatch(setCompleted(false))
                                            }}
                                            className={cn(
                                                'flex items-center gap-x-2 line-clamp-1 hover:dark:text-sky-blue'
                                            )}
                                        >
                                            {session.name}
                                        </Link>
                                    ))}
                                </div>
                            </div>
                        ))}
                </div>
            </SheetContent>
        </Sheet>
    )
}

export default SearchHistory
