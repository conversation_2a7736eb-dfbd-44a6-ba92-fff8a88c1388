import { useRef, useState, useEffect } from 'react'
import { toast } from 'sonner'

import { Icon } from '../ui/icon'
import {
    selectResultUrl,
    selectActiveTab,
    selectIsSandboxIframeAwake,
    setSandboxIframeAwake,
    useAppSelector,
    useAppDispatch
} from '@/state'
import { TAB } from '@/typings/agent'
import { Button } from '../ui/button'

interface AgentResultProps {
    className?: string
}

const AgentResult = ({ className }: AgentResultProps) => {
    const iframeRef = useRef<HTMLIFrameElement>(null)
    const [iframeKey, setIframeKey] = useState(0)
    const dispatch = useAppDispatch()

    const resultUrl = useAppSelector(selectResultUrl)
    const activeTab = useAppSelector(selectActiveTab)
    const isSandboxIframeAwake = useAppSelector(selectIsSandboxIframeAwake)

    const handleCopy = () => {
        if (!resultUrl) return
        navigator.clipboard.writeText(resultUrl)
        toast.success('Copied to clipboard')
    }

    const handleRefresh = () => {
        setIframeKey((prev) => prev + 1)
    }

    useEffect(() => {
        if (activeTab === TAB.RESULT) {
            handleRefresh()
        }
    }, [activeTab])

    const isE2bLink = (url: string): boolean => {
        try {
            const parsed = new URL(url)
            return (
                parsed.hostname.includes('e2b') ||
                parsed.hostname.includes('e2b-')
            )
        } catch {
            return false
        }
    }

    const detectUrlType = (url: string): 'website' | 'image' | 'video' => {
        try {
            const parsed = new URL(url)
            const pathname = parsed.pathname.toLowerCase()

            // Common image extensions
            const imageExt = [
                '.png',
                '.jpg',
                '.jpeg',
                '.gif',
                '.bmp',
                '.webp',
                '.svg'
            ]
            // Common video extensions
            const videoExt = [
                '.mp4',
                '.mov',
                '.avi',
                '.mkv',
                '.webm',
                '.flv',
                '.wmv'
            ]

            if (imageExt.some((ext) => pathname.endsWith(ext))) {
                return 'image'
            }
            if (videoExt.some((ext) => pathname.endsWith(ext))) {
                return 'video'
            }

            return 'website'
        } catch {
            return 'website' // fallback if URL is invalid
        }
    }

    const handleAwakeClick = () => {
        dispatch(setSandboxIframeAwake(true))
    }

    if (!resultUrl) return null

    if (isE2bLink(resultUrl) && !isSandboxIframeAwake)
        return (
            <div className="w-full h-full flex-1 flex items-center justify-center bg-white dark:bg-charcoal">
                <div className="flex flex-col items-center gap-6">
                    <Icon name="sleep" className="fill-black dark:fill-white" />
                    <div className="text-center dark:text-sky-blue-2">
                        <h2 className="text-[32px] font-bold mb-1">
                            I’m already sleep
                        </h2>
                        <p className="text-2xl">for power saving</p>
                    </div>
                    <Button
                        onClick={handleAwakeClick}
                        size="xl"
                        className="bg-sky-blue text-black font-bold w-full max-w-[247px]"
                    >
                        Awake me up
                    </Button>
                </div>
            </div>
        )

    return (
        <div
            className={`flex flex-col items-center w-full h-full bg-white dark:bg-charcoal ${className}`}
        >
            <div className="w-full flex items-center justify-between pl-6 pr-4 py-2 gap-4 overflow-hidden border-b border-white/30">
                <div className="rounded-lg w-full flex items-center gap-4 group transition-colors">
                    <button className="cursor-pointer" onClick={handleRefresh}>
                        <Icon
                            name="refresh"
                            className="size-5 stroke-black dark:stroke-white"
                        />
                    </button>
                    <span className="text-sm text-black bg-[#f4f4f4] dark:bg-white line-clamp-1 break-all flex-1 font-bold px-4 py-1 rounded-sm">
                        {resultUrl}
                    </span>
                </div>
                <div className="flex items-center gap-4">
                    <button className="cursor-pointer" onClick={handleCopy}>
                        <Icon
                            name="copy"
                            className="size-5 fill-black dark:fill-white"
                        />
                    </button>
                    <button
                        className="cursor-pointer"
                        onClick={() => window.open(resultUrl, '_blank')}
                    >
                        <Icon
                            name="maximize"
                            className="size-5 fill-black dark:fill-white"
                        />
                    </button>
                </div>
            </div>
            {detectUrlType(resultUrl) === 'image' ? (
                <img
                    src={resultUrl}
                    className="w-full h-full object-contain object-top flex-1"
                />
            ) : detectUrlType(resultUrl) === 'video' ? (
                <video
                    loop
                    muted
                    controls
                    src={resultUrl}
                    className="w-full h-full object-contain object-top flex-1"
                />
            ) : (
                <iframe
                    key={iframeKey}
                    ref={iframeRef}
                    src={resultUrl}
                    className="w-full h-full flex-1"
                />
            )}
        </div>
    )
}

export default AgentResult
