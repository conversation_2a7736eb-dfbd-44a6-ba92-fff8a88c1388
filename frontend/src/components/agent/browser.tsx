import React, { useMemo } from 'react'

import Markdown from '../markdown'
import { Icon } from '../ui/icon'

interface BrowserProps {
    className?: string
    contentClassName?: string
    markdownClassName?: string
    url?: string
    screenshot?: string
    screenshotClassName?: string
    raw?: string
    isHideHeader?: boolean
    isVideoUrl?: boolean
}

const Browser = React.memo(
    ({
        className,
        url,
        screenshot,
        screenshotClassName,
        raw,
        isHideHeader,
        contentClassName,
        markdownClassName,
        isVideoUrl
    }: BrowserProps) => {
        const isVideo = useMemo(
            () => isVideoUrl || url?.endsWith('.mp4') || url?.endsWith('.mov'),
            [url, isVideoUrl]
        )

        if (!url) return

        return (
            <div
                className={`h-[calc(100vh-178px)] rounded-xl overflow-hidden ${className}`}
            >
                {!isHideHeader && (
                    <div className="flex items-center gap-3 px-3 py-2.5 bg-white dark:bg-charcoal border-b border-grey-2/30 dark:border-white/30">
                        <div className="flex-1 flex items-center overflow-hidden">
                            <div className="px-3 py-1.5 rounded-lg w-full flex items-center gap-2 group transition-colors opacity-30">
                                <Icon
                                    name="document-text"
                                    className="size-5 fill-black dark:fill-white"
                                />
                                <span className="text-sm dark:text-white line-clamp-1 flex-1 font-bold">
                                    {url}
                                </span>
                            </div>
                        </div>
                        <div className="flex items-center gap-1">
                            <button
                                className="p-1.5 rounded-md cursor-pointer"
                                onClick={() => window.open(url, '_blank')}
                            >
                                <Icon
                                    name="maximize"
                                    className="h-4 w-4 fill-black dark:fill-white"
                                />
                            </button>
                        </div>
                    </div>
                )}
                <div
                    className={`bg-white dark:bg-black p-6 ${contentClassName}`}
                >
                    {isVideo ? (
                        <video
                            src={url}
                            loop
                            muted
                            controls
                            className={`w-full h-full object-contain object-top rounded-xl overflow-hidden ${screenshotClassName}`}
                        />
                    ) : (
                        screenshot && (
                            <img
                                src={
                                    typeof screenshot === 'string' && screenshot.startsWith('http')
                                        ? screenshot
                                        : `data:image/jpeg;base64,${screenshot}`
                                }
                                alt="Browser"
                                className={`w-full h-full object-contain object-top rounded-xl overflow-hidden ${screenshotClassName}`}
                            />
                        )
                    )}
                    {raw && (
                        <div
                            className={`p-4 overflow-auto h-[calc(100vh-234px)] ${markdownClassName}`}
                        >
                            <Markdown>{raw}</Markdown>
                        </div>
                    )}
                </div>
            </div>
        )
    }
)

Browser.displayName = 'Browser'

export default Browser
